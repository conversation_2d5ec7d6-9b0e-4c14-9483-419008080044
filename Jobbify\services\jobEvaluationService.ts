/**
 * AI-powered job evaluation service using OpenRouter API
 * Provides intelligent analysis and insights for job descriptions
 */

interface JobEvaluation {
  overallScore: number; // 1-10 rating
  strengths: string[];
  concerns: string[];
  salaryEstimate: string;
  careerGrowth: string;
  workLifeBalance: string;
  companyReputation: string;
  skillsRequired: string[];
  aiSummary: string;
  // Enhanced evaluation fields
  remoteWorkFriendly: string;
  companyCulture: string;
  jobSecurity: string;
  learningOpportunities: string;
  competitiveAdvantages: string[];
  redFlags: string[];
  industryOutlook: string;
  roleComplexity: 'entry' | 'mid' | 'senior' | 'executive';
  timeCommitment: string;
  travelRequirements: string;
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class JobEvaluationService {
  private apiKey = '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';
  private baseUrl = 'https://openrouter.ai/api/v1';
  private model = 'google/gemini-2.0-flash-exp:free';

  /**
   * Evaluate a job description using AI
   */
  async evaluateJob(jobTitle: string, company: string, description: string, location: string = ''): Promise<JobEvaluation> {
    try {
      const prompt = this.createEvaluationPrompt(jobTitle, company, description, location);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://hireista.app',
          'X-Title': 'Hireista Job Platform'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data: OpenRouterResponse = await response.json();
      const aiResponse = data.choices[0]?.message?.content;

      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      return this.parseEvaluationResponse(aiResponse);

    } catch (error) {
      console.error('Error evaluating job with AI:', error);
      return this.getFallbackEvaluation(jobTitle, company);
    }
  }

  /**
   * Create a comprehensive evaluation prompt
   */
  private createEvaluationPrompt(jobTitle: string, company: string, description: string, location: string): string {
    return `Analyze this job posting and provide a comprehensive evaluation. Respond with a JSON object containing the following fields:

Job Details:
- Title: ${jobTitle}
- Company: ${company}
- Location: ${location}
- Description: ${description}

Please analyze and respond with this exact JSON structure:
{
  "overallScore": <number 1-10>,
  "strengths": ["strength1", "strength2", "strength3"],
  "concerns": ["concern1", "concern2"],
  "salaryEstimate": "estimated salary range with currency",
  "careerGrowth": "brief assessment of growth potential",
  "workLifeBalance": "assessment of work-life balance",
  "companyReputation": "brief company reputation assessment",
  "skillsRequired": ["skill1", "skill2", "skill3"],
  "aiSummary": "2-3 sentence summary of why this job is worth considering",
  "remoteWorkFriendly": "assessment of remote work options",
  "companyCulture": "brief assessment of company culture",
  "jobSecurity": "assessment of job stability and security",
  "learningOpportunities": "assessment of learning and development opportunities",
  "competitiveAdvantages": ["advantage1", "advantage2"],
  "redFlags": ["flag1", "flag2"],
  "industryOutlook": "brief industry outlook assessment",
  "roleComplexity": "entry|mid|senior|executive",
  "timeCommitment": "assessment of expected time commitment",
  "travelRequirements": "assessment of travel requirements"
}

Focus on:
1. Overall job quality and appeal (1-10 score)
2. Key strengths and unique opportunities
3. Potential red flags or concerns to watch for
4. Realistic salary expectations for this role and location
5. Career growth and advancement potential
6. Work-life balance indicators and flexibility
7. Company reputation, culture, and stability
8. Essential skills and experience needed
9. Remote work friendliness and flexibility
10. Learning and professional development opportunities
11. Competitive advantages this role offers
12. Industry outlook and future prospects
13. Role complexity level and seniority
14. Expected time commitment and work intensity
15. Travel requirements and location flexibility

Be realistic, helpful, and provide actionable insights that help job seekers make informed decisions.`;
  }

  /**
   * Parse the AI response into structured data
   */
  private parseEvaluationResponse(response: string): JobEvaluation {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);

        return {
          overallScore: Math.max(1, Math.min(10, parsed.overallScore || 7)),
          strengths: Array.isArray(parsed.strengths) ? parsed.strengths : ['Competitive opportunity', 'Professional growth', 'Skill development'],
          concerns: Array.isArray(parsed.concerns) ? parsed.concerns : ['Limited information available'],
          salaryEstimate: parsed.salaryEstimate || 'Competitive salary',
          careerGrowth: parsed.careerGrowth || 'Good growth potential',
          workLifeBalance: parsed.workLifeBalance || 'Standard work-life balance',
          companyReputation: parsed.companyReputation || 'Established company',
          skillsRequired: Array.isArray(parsed.skillsRequired) ? parsed.skillsRequired : ['Professional experience', 'Communication skills', 'Technical aptitude'],
          aiSummary: parsed.aiSummary || 'This position offers a solid opportunity for professional growth with competitive compensation.',
          // Enhanced fields with fallbacks
          remoteWorkFriendly: parsed.remoteWorkFriendly || 'Standard office-based role',
          companyCulture: parsed.companyCulture || 'Professional work environment',
          jobSecurity: parsed.jobSecurity || 'Stable employment opportunity',
          learningOpportunities: parsed.learningOpportunities || 'Standard professional development',
          competitiveAdvantages: Array.isArray(parsed.competitiveAdvantages) ? parsed.competitiveAdvantages : ['Market competitive role', 'Professional growth'],
          redFlags: Array.isArray(parsed.redFlags) ? parsed.redFlags : [],
          industryOutlook: parsed.industryOutlook || 'Stable industry outlook',
          roleComplexity: ['entry', 'mid', 'senior', 'executive'].includes(parsed.roleComplexity) ? parsed.roleComplexity : 'mid',
          timeCommitment: parsed.timeCommitment || 'Standard full-time commitment',
          travelRequirements: parsed.travelRequirements || 'Minimal travel expected'
        };
      }
    } catch (error) {
      console.warn('Failed to parse AI evaluation response:', error);
    }

    // Fallback to extracting information from free text
    return this.extractFromFreeText(response);
  }

  /**
   * Extract evaluation data from free text response
   */
  private extractFromFreeText(response: string): JobEvaluation {
    const scoreMatch = response.match(/score[:\s]*(\d+)/i);
    const score = scoreMatch ? parseInt(scoreMatch[1]) : 7;

    return {
      overallScore: Math.max(1, Math.min(10, score)),
      strengths: this.extractListItems(response, /strengths?[:\s]*([^.]*)/i) || ['Professional opportunity', 'Career development', 'Competitive role'],
      concerns: this.extractListItems(response, /concerns?[:\s]*([^.]*)/i) || ['Standard considerations'],
      salaryEstimate: this.extractValue(response, /salary[:\s]*([^.]*)/i) || 'Competitive compensation',
      careerGrowth: this.extractValue(response, /growth[:\s]*([^.]*)/i) || 'Good advancement opportunities',
      workLifeBalance: this.extractValue(response, /balance[:\s]*([^.]*)/i) || 'Standard work-life balance',
      companyReputation: this.extractValue(response, /reputation[:\s]*([^.]*)/i) || 'Reputable organization',
      skillsRequired: this.extractListItems(response, /skills?[:\s]*([^.]*)/i) || ['Professional experience', 'Strong communication', 'Technical skills'],
      aiSummary: response.slice(0, 200) + '...' || 'This position offers excellent opportunities for professional growth and development.',
      // Enhanced fields with fallbacks
      remoteWorkFriendly: this.extractValue(response, /remote[:\s]*([^.]*)/i) || 'Standard office-based role',
      companyCulture: this.extractValue(response, /culture[:\s]*([^.]*)/i) || 'Professional work environment',
      jobSecurity: this.extractValue(response, /security[:\s]*([^.]*)/i) || 'Stable employment opportunity',
      learningOpportunities: this.extractValue(response, /learning[:\s]*([^.]*)/i) || 'Standard professional development',
      competitiveAdvantages: this.extractListItems(response, /advantages?[:\s]*([^.]*)/i) || ['Market competitive role', 'Professional growth'],
      redFlags: this.extractListItems(response, /red.flags?[:\s]*([^.]*)/i) || [],
      industryOutlook: this.extractValue(response, /industry[:\s]*([^.]*)/i) || 'Stable industry outlook',
      roleComplexity: this.extractRoleComplexity(response),
      timeCommitment: this.extractValue(response, /time[:\s]*([^.]*)/i) || 'Standard full-time commitment',
      travelRequirements: this.extractValue(response, /travel[:\s]*([^.]*)/i) || 'Minimal travel expected'
    };
  }

  /**
   * Extract list items from text
   */
  private extractListItems(text: string, regex: RegExp): string[] | null {
    const match = text.match(regex);
    if (match) {
      return match[1].split(/[,;]/).map(item => item.trim()).filter(item => item.length > 0);
    }
    return null;
  }

  /**
   * Extract single value from text
   */
  private extractValue(text: string, regex: RegExp): string | null {
    const match = text.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * Extract role complexity from text
   */
  private extractRoleComplexity(text: string): 'entry' | 'mid' | 'senior' | 'executive' {
    const lowerText = text.toLowerCase();
    if (lowerText.includes('executive') || lowerText.includes('director') || lowerText.includes('vp') || lowerText.includes('chief')) {
      return 'executive';
    }
    if (lowerText.includes('senior') || lowerText.includes('lead') || lowerText.includes('principal')) {
      return 'senior';
    }
    if (lowerText.includes('entry') || lowerText.includes('junior') || lowerText.includes('associate')) {
      return 'entry';
    }
    return 'mid';
  }

  /**
   * Provide fallback evaluation when AI fails
   */
  private getFallbackEvaluation(jobTitle: string, company: string): JobEvaluation {
    return {
      overallScore: 7,
      strengths: [
        'Opportunity to work with a established company',
        'Professional development potential',
        'Competitive role in the market'
      ],
      concerns: [
        'Limited specific details available',
        'Standard employment considerations'
      ],
      salaryEstimate: 'Competitive salary based on experience',
      careerGrowth: 'Good potential for advancement',
      workLifeBalance: 'Standard work-life balance expected',
      companyReputation: `${company} is an established organization`,
      skillsRequired: [
        'Relevant professional experience',
        'Strong communication skills',
        'Industry knowledge'
      ],
      aiSummary: `This ${jobTitle} position at ${company} offers a solid opportunity for professional growth with competitive compensation and benefits.`,
      // Enhanced fallback fields
      remoteWorkFriendly: 'Standard office-based role',
      companyCulture: 'Professional work environment',
      jobSecurity: 'Stable employment opportunity',
      learningOpportunities: 'Standard professional development opportunities',
      competitiveAdvantages: ['Established company', 'Professional growth potential'],
      redFlags: [],
      industryOutlook: 'Stable industry outlook',
      roleComplexity: this.extractRoleComplexity(jobTitle),
      timeCommitment: 'Standard full-time commitment',
      travelRequirements: 'Minimal travel expected'
    };
  }

  /**
   * Format evaluation for display in job panels
   */
  formatEvaluationForDisplay(evaluation: JobEvaluation): string {
    const scoreEmoji = evaluation.overallScore >= 8 ? '🌟' : evaluation.overallScore >= 6 ? '⭐' : '✨';
    const complexityEmoji = {
      'entry': '🌱',
      'mid': '🚀',
      'senior': '👑',
      'executive': '💎'
    }[evaluation.roleComplexity] || '🚀';

    return `
**AI Job Evaluation ${scoreEmoji} (${evaluation.overallScore}/10)**

${evaluation.aiSummary}

**💼 Role Overview**
• **Level:** ${complexityEmoji} ${evaluation.roleComplexity.charAt(0).toUpperCase() + evaluation.roleComplexity.slice(1)} Level
• **Time Commitment:** ${evaluation.timeCommitment}
• **Travel:** ${evaluation.travelRequirements}

**💰 Compensation & Benefits**
• **Estimated Salary:** ${evaluation.salaryEstimate}
• **Industry Outlook:** ${evaluation.industryOutlook}

**🏢 Company & Culture**
• **Company Reputation:** ${evaluation.companyReputation}
• **Work Culture:** ${evaluation.companyCulture}
• **Job Security:** ${evaluation.jobSecurity}

**📈 Growth & Development**
• **Career Growth:** ${evaluation.careerGrowth}
• **Learning Opportunities:** ${evaluation.learningOpportunities}
• **Work-Life Balance:** ${evaluation.workLifeBalance}

**🌐 Work Flexibility**
• **Remote Work:** ${evaluation.remoteWorkFriendly}

**✅ Key Strengths:**
${evaluation.strengths.map(strength => `• ${strength}`).join('\n')}

**🎯 Competitive Advantages:**
${evaluation.competitiveAdvantages.map(advantage => `• ${advantage}`).join('\n')}

**⚠️ Considerations:**
${evaluation.concerns.map(concern => `• ${concern}`).join('\n')}

${evaluation.redFlags.length > 0 ? `**🚩 Red Flags:**
${evaluation.redFlags.map(flag => `• ${flag}`).join('\n')}

` : ''}**🎯 Skills Needed:**
${evaluation.skillsRequired.map(skill => `• ${skill}`).join('\n')}
    `.trim();
  }
}

export const jobEvaluationService = new JobEvaluationService();
export { JobEvaluation };
