/**
 * AI-powered job evaluation service using OpenRouter API
 * Provides intelligent analysis and insights for job descriptions
 */

interface JobEvaluation {
  overallScore: number; // 1-10 rating
  strengths: string[];
  concerns: string[];
  salaryEstimate: string;
  careerGrowth: string;
  workLifeBalance: string;
  companyReputation: string;
  skillsRequired: string[];
  aiSummary: string;
}

interface OpenRouterResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

class JobEvaluationService {
  private apiKey = '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';
  private baseUrl = 'https://openrouter.ai/api/v1';
  private model = 'google/gemini-2.0-flash-exp:free';

  /**
   * Evaluate a job description using AI
   */
  async evaluateJob(jobTitle: string, company: string, description: string, location: string = ''): Promise<JobEvaluation> {
    try {
      const prompt = this.createEvaluationPrompt(jobTitle, company, description, location);
      
      const response = await fetch(`${this.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': 'https://hireista.app',
          'X-Title': 'Hireista Job Platform'
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 1000
        })
      });

      if (!response.ok) {
        throw new Error(`OpenRouter API error: ${response.status}`);
      }

      const data: OpenRouterResponse = await response.json();
      const aiResponse = data.choices[0]?.message?.content;

      if (!aiResponse) {
        throw new Error('No response from AI');
      }

      return this.parseEvaluationResponse(aiResponse);

    } catch (error) {
      console.error('Error evaluating job with AI:', error);
      return this.getFallbackEvaluation(jobTitle, company);
    }
  }

  /**
   * Create a comprehensive evaluation prompt
   */
  private createEvaluationPrompt(jobTitle: string, company: string, description: string, location: string): string {
    return `Analyze this job posting and provide a comprehensive evaluation. Respond with a JSON object containing the following fields:

Job Details:
- Title: ${jobTitle}
- Company: ${company}
- Location: ${location}
- Description: ${description}

Please analyze and respond with this exact JSON structure:
{
  "overallScore": <number 1-10>,
  "strengths": ["strength1", "strength2", "strength3"],
  "concerns": ["concern1", "concern2"],
  "salaryEstimate": "estimated salary range",
  "careerGrowth": "brief assessment of growth potential",
  "workLifeBalance": "assessment of work-life balance",
  "companyReputation": "brief company reputation assessment",
  "skillsRequired": ["skill1", "skill2", "skill3"],
  "aiSummary": "2-3 sentence summary of why this job is worth considering"
}

Focus on:
1. Overall job quality and appeal (1-10 score)
2. Key strengths and opportunities
3. Potential red flags or concerns
4. Realistic salary expectations for this role
5. Career growth potential
6. Work-life balance indicators
7. Company reputation and stability
8. Essential skills needed
9. A compelling summary for job seekers

Be realistic, helpful, and provide actionable insights.`;
  }

  /**
   * Parse the AI response into structured data
   */
  private parseEvaluationResponse(response: string): JobEvaluation {
    try {
      // Try to extract JSON from the response
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const parsed = JSON.parse(jsonMatch[0]);
        
        return {
          overallScore: Math.max(1, Math.min(10, parsed.overallScore || 7)),
          strengths: Array.isArray(parsed.strengths) ? parsed.strengths : ['Competitive opportunity', 'Professional growth', 'Skill development'],
          concerns: Array.isArray(parsed.concerns) ? parsed.concerns : ['Limited information available'],
          salaryEstimate: parsed.salaryEstimate || 'Competitive salary',
          careerGrowth: parsed.careerGrowth || 'Good growth potential',
          workLifeBalance: parsed.workLifeBalance || 'Standard work-life balance',
          companyReputation: parsed.companyReputation || 'Established company',
          skillsRequired: Array.isArray(parsed.skillsRequired) ? parsed.skillsRequired : ['Professional experience', 'Communication skills', 'Technical aptitude'],
          aiSummary: parsed.aiSummary || 'This position offers a solid opportunity for professional growth with competitive compensation.'
        };
      }
    } catch (error) {
      console.warn('Failed to parse AI evaluation response:', error);
    }

    // Fallback to extracting information from free text
    return this.extractFromFreeText(response);
  }

  /**
   * Extract evaluation data from free text response
   */
  private extractFromFreeText(response: string): JobEvaluation {
    const scoreMatch = response.match(/score[:\s]*(\d+)/i);
    const score = scoreMatch ? parseInt(scoreMatch[1]) : 7;

    return {
      overallScore: Math.max(1, Math.min(10, score)),
      strengths: this.extractListItems(response, /strengths?[:\s]*([^.]*)/i) || ['Professional opportunity', 'Career development', 'Competitive role'],
      concerns: this.extractListItems(response, /concerns?[:\s]*([^.]*)/i) || ['Standard considerations'],
      salaryEstimate: this.extractValue(response, /salary[:\s]*([^.]*)/i) || 'Competitive compensation',
      careerGrowth: this.extractValue(response, /growth[:\s]*([^.]*)/i) || 'Good advancement opportunities',
      workLifeBalance: this.extractValue(response, /balance[:\s]*([^.]*)/i) || 'Standard work-life balance',
      companyReputation: this.extractValue(response, /reputation[:\s]*([^.]*)/i) || 'Reputable organization',
      skillsRequired: this.extractListItems(response, /skills?[:\s]*([^.]*)/i) || ['Professional experience', 'Strong communication', 'Technical skills'],
      aiSummary: response.slice(0, 200) + '...' || 'This position offers excellent opportunities for professional growth and development.'
    };
  }

  /**
   * Extract list items from text
   */
  private extractListItems(text: string, regex: RegExp): string[] | null {
    const match = text.match(regex);
    if (match) {
      return match[1].split(/[,;]/).map(item => item.trim()).filter(item => item.length > 0);
    }
    return null;
  }

  /**
   * Extract single value from text
   */
  private extractValue(text: string, regex: RegExp): string | null {
    const match = text.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * Provide fallback evaluation when AI fails
   */
  private getFallbackEvaluation(jobTitle: string, company: string): JobEvaluation {
    return {
      overallScore: 7,
      strengths: [
        'Opportunity to work with a established company',
        'Professional development potential',
        'Competitive role in the market'
      ],
      concerns: [
        'Limited specific details available',
        'Standard employment considerations'
      ],
      salaryEstimate: 'Competitive salary based on experience',
      careerGrowth: 'Good potential for advancement',
      workLifeBalance: 'Standard work-life balance expected',
      companyReputation: `${company} is an established organization`,
      skillsRequired: [
        'Relevant professional experience',
        'Strong communication skills',
        'Industry knowledge'
      ],
      aiSummary: `This ${jobTitle} position at ${company} offers a solid opportunity for professional growth with competitive compensation and benefits.`
    };
  }

  /**
   * Format evaluation for display in job panels
   */
  formatEvaluationForDisplay(evaluation: JobEvaluation): string {
    const scoreEmoji = evaluation.overallScore >= 8 ? '🌟' : evaluation.overallScore >= 6 ? '⭐' : '✨';
    
    return `
**AI Job Evaluation ${scoreEmoji} (${evaluation.overallScore}/10)**

${evaluation.aiSummary}

**💰 Estimated Salary:** ${evaluation.salaryEstimate}

**📈 Career Growth:** ${evaluation.careerGrowth}

**⚖️ Work-Life Balance:** ${evaluation.workLifeBalance}

**🏢 Company Reputation:** ${evaluation.companyReputation}

**✅ Key Strengths:**
${evaluation.strengths.map(strength => `• ${strength}`).join('\n')}

**⚠️ Considerations:**
${evaluation.concerns.map(concern => `• ${concern}`).join('\n')}

**🎯 Skills Needed:**
${evaluation.skillsRequired.map(skill => `• ${skill}`).join('\n')}
    `.trim();
  }
}

export const jobEvaluationService = new JobEvaluationService();
export { JobEvaluation };
