// Simple test script to verify the enhanced AI evaluation system
const { jobEvaluationService } = require('./services/jobEvaluationService');

async function testAIEvaluation() {
  console.log('Testing Enhanced AI Job Evaluation System...\n');
  
  // Test job data
  const testJob = {
    title: 'Senior Software Engineer',
    company: 'TechCorp Inc',
    location: 'San Francisco, CA (Remote)',
    description: `We are seeking a Senior Software Engineer to join our growing team. 
    You will be responsible for developing scalable web applications using React, Node.js, and AWS.
    
    Responsibilities:
    - Design and implement new features
    - Collaborate with cross-functional teams
    - <PERSON>tor junior developers
    - Participate in code reviews
    
    Requirements:
    - 5+ years of software development experience
    - Strong knowledge of JavaScript, React, and Node.js
    - Experience with cloud platforms (AWS preferred)
    - Excellent communication skills
    
    Benefits:
    - Competitive salary ($120K-$160K)
    - Health insurance
    - 401k matching
    - Flexible work arrangements
    - Professional development budget`
  };

  try {
    console.log('Evaluating job:', testJob.title, 'at', testJob.company);
    console.log('Location:', testJob.location);
    console.log('---\n');

    const evaluation = await jobEvaluationService.evaluateJob(
      testJob.title,
      testJob.company,
      testJob.description,
      testJob.location
    );

    console.log('✅ AI Evaluation completed successfully!\n');
    
    console.log('📊 EVALUATION RESULTS:');
    console.log('='.repeat(50));
    console.log(`Overall Score: ${evaluation.overallScore}/10`);
    console.log(`Role Complexity: ${evaluation.roleComplexity}`);
    console.log(`Salary Estimate: ${evaluation.salaryEstimate}`);
    console.log(`Remote Work: ${evaluation.remoteWorkFriendly}`);
    console.log(`Company Culture: ${evaluation.companyCulture}`);
    console.log(`Job Security: ${evaluation.jobSecurity}`);
    console.log(`Learning Opportunities: ${evaluation.learningOpportunities}`);
    console.log(`Career Growth: ${evaluation.careerGrowth}`);
    console.log(`Work-Life Balance: ${evaluation.workLifeBalance}`);
    console.log(`Industry Outlook: ${evaluation.industryOutlook}`);
    console.log(`Time Commitment: ${evaluation.timeCommitment}`);
    console.log(`Travel Requirements: ${evaluation.travelRequirements}`);
    
    console.log('\n💪 STRENGTHS:');
    evaluation.strengths.forEach((strength, index) => {
      console.log(`  ${index + 1}. ${strength}`);
    });
    
    console.log('\n🌟 COMPETITIVE ADVANTAGES:');
    evaluation.competitiveAdvantages.forEach((advantage, index) => {
      console.log(`  ${index + 1}. ${advantage}`);
    });
    
    console.log('\n⚠️  CONSIDERATIONS:');
    evaluation.concerns.forEach((concern, index) => {
      console.log(`  ${index + 1}. ${concern}`);
    });
    
    if (evaluation.redFlags.length > 0) {
      console.log('\n🚩 RED FLAGS:');
      evaluation.redFlags.forEach((flag, index) => {
        console.log(`  ${index + 1}. ${flag}`);
      });
    }
    
    console.log('\n🎯 SKILLS REQUIRED:');
    evaluation.skillsRequired.forEach((skill, index) => {
      console.log(`  ${index + 1}. ${skill}`);
    });
    
    console.log('\n📝 AI SUMMARY:');
    console.log(`"${evaluation.aiSummary}"`);
    
    console.log('\n' + '='.repeat(50));
    console.log('📱 FORMATTED DISPLAY:');
    console.log('='.repeat(50));
    
    const formattedDisplay = jobEvaluationService.formatEvaluationForDisplay(evaluation);
    console.log(formattedDisplay);
    
    console.log('\n✅ Test completed successfully!');
    console.log('The enhanced AI evaluation system is working properly.');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Full error:', error);
  }
}

// Run the test
testAIEvaluation();
