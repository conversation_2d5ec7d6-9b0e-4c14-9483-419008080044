import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ActivityIndicator } from 'react-native';
import { MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { Job } from '@/context/AppContext';
import { 
  evaluateJobWithAI, 
  AIJobEvaluation, 
  getEvaluationScoreColor, 
  getFitLevelIcon 
} from '@/services/aiEvaluationService';

interface AIJobEvaluationProps {
  job: Job;
  themeColors: {
    background: string;
    card: string;
    text: string;
    textSecondary: string;
    tint: string;
  };
  style?: any;
}

export const AIJobEvaluationComponent: React.FC<AIJobEvaluationProps> = ({ 
  job, 
  themeColors, 
  style 
}) => {
  const [evaluation, setEvaluation] = useState<AIJobEvaluation | null>(job.aiEvaluation || null);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load AI evaluation if not already present
  useEffect(() => {
    if (!evaluation && !isLoading) {
      loadEvaluation();
    }
  }, [job.id]);

  const loadEvaluation = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await evaluateJobWithAI(job);
      setEvaluation(result);
      
      // Update the job object with the evaluation for caching
      job.aiEvaluation = result;
    } catch (err) {
      console.error('Failed to load AI evaluation:', err);
      setError('Failed to load AI evaluation');
    } finally {
      setIsLoading(false);
    }
  };

  const retryEvaluation = () => {
    setError(null);
    loadEvaluation();
  };

  if (isLoading) {
    return (
      <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <MaterialIcons name="psychology" size={20} color={themeColors.tint} />
            <Text style={[styles.title, { color: themeColors.text }]}>AI Job Analysis</Text>
          </View>
          <ActivityIndicator size="small" color={themeColors.tint} />
        </View>
        <Text style={[styles.loadingText, { color: themeColors.textSecondary }]}>
          Analyzing job posting...
        </Text>
      </View>
    );
  }

  if (error) {
    return (
      <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <MaterialIcons name="error-outline" size={20} color="#F44336" />
            <Text style={[styles.title, { color: themeColors.text }]}>AI Analysis</Text>
          </View>
          <TouchableOpacity onPress={retryEvaluation} style={styles.retryButton}>
            <MaterialIcons name="refresh" size={16} color={themeColors.tint} />
          </TouchableOpacity>
        </View>
        <Text style={[styles.errorText, { color: '#F44336' }]}>
          {error}
        </Text>
      </View>
    );
  }

  if (!evaluation) {
    return null;
  }

  const scoreColor = getEvaluationScoreColor(evaluation.score);
  const fitIcon = getFitLevelIcon(evaluation.fit_level);

  return (
    <View style={[styles.container, { backgroundColor: themeColors.card }, style]}>
      {/* Header with score */}
      <TouchableOpacity 
        style={styles.header}
        onPress={() => setIsExpanded(!isExpanded)}
        activeOpacity={0.7}
      >
        <View style={styles.titleRow}>
          <MaterialIcons name="psychology" size={20} color={themeColors.tint} />
          <Text style={[styles.title, { color: themeColors.text }]}>AI Job Analysis</Text>
          <Text style={styles.fitIcon}>{fitIcon}</Text>
        </View>
        <View style={styles.scoreContainer}>
          <Text style={[styles.score, { color: scoreColor }]}>
            {evaluation.score}/100
          </Text>
          <MaterialIcons 
            name={isExpanded ? "expand-less" : "expand-more"} 
            size={20} 
            color={themeColors.textSecondary} 
          />
        </View>
      </TouchableOpacity>

      {/* Summary (always visible) */}
      <Text style={[styles.summary, { color: themeColors.textSecondary }]}>
        {evaluation.summary}
      </Text>

      {/* Expandable details */}
      {isExpanded && (
        <View style={styles.expandedContent}>
          {/* Strengths */}
          {evaluation.strengths.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialIcons name="thumb-up" size={16} color="#4CAF50" />
                <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Strengths</Text>
              </View>
              {evaluation.strengths.slice(0, 3).map((strength, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={[styles.bullet, { color: '#4CAF50' }]}>•</Text>
                  <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                    {strength}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Concerns */}
          {evaluation.concerns.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialIcons name="warning" size={16} color="#FF9800" />
                <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Considerations</Text>
              </View>
              {evaluation.concerns.slice(0, 3).map((concern, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={[styles.bullet, { color: '#FF9800' }]}>•</Text>
                  <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                    {concern}
                  </Text>
                </View>
              ))}
            </View>
          )}

          {/* Recommendations */}
          {evaluation.recommendations.length > 0 && (
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <MaterialIcons name="lightbulb" size={16} color={themeColors.tint} />
                <Text style={[styles.sectionTitle, { color: themeColors.text }]}>Recommendations</Text>
              </View>
              {evaluation.recommendations.slice(0, 2).map((recommendation, index) => (
                <View key={index} style={styles.bulletPoint}>
                  <Text style={[styles.bullet, { color: themeColors.tint }]}>•</Text>
                  <Text style={[styles.bulletText, { color: themeColors.textSecondary }]}>
                    {recommendation}
                  </Text>
                </View>
              ))}
            </View>
          )}
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
    flex: 1,
  },
  fitIcon: {
    fontSize: 16,
    marginLeft: 8,
  },
  scoreContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  score: {
    fontSize: 16,
    fontWeight: '700',
    marginRight: 4,
  },
  summary: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 8,
  },
  loadingText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  errorText: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  retryButton: {
    padding: 4,
  },
  expandedContent: {
    marginTop: 8,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  section: {
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 6,
  },
  bulletPoint: {
    flexDirection: 'row',
    marginBottom: 4,
    alignItems: 'flex-start',
  },
  bullet: {
    fontSize: 14,
    marginRight: 8,
    fontWeight: 'bold',
    marginTop: 1,
  },
  bulletText: {
    fontSize: 14,
    flex: 1,
    lineHeight: 18,
  },
});
