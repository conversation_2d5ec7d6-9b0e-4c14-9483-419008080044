import React from 'react';
import { Stack } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';

export default function OnboardingLayout() {
  const { theme } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  return (
    <Stack
      screenOptions={{
        headerShown: false,
        contentStyle: { backgroundColor: themeColors.background },
      }}
    >
      <Stack.Screen name="job-seeker" />
      <Stack.Screen name="career-preferences" />
      <Stack.Screen name="job-preferences" />
      <Stack.Screen name="service-provider" />
    </Stack>
  );
}