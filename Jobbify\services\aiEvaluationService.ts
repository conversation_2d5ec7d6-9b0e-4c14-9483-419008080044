import { Job } from '@/context/AppContext';

const OPENROUTER_API_KEY = '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';
const OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1';

export interface AIJobEvaluation {
  score: number; // 0-100 rating
  strengths: string[];
  concerns: string[];
  summary: string;
  recommendations: string[];
  fit_level: 'excellent' | 'good' | 'fair' | 'poor';
}

/**
 * Evaluate a job using AI to provide insights about job fit, compensation, and requirements
 */
export const evaluateJobWithAI = async (job: Job, userProfile?: any): Promise<AIJobEvaluation> => {
  try {
    console.log(`Evaluating job with AI: ${job.title} at ${job.company}`);

    // Construct the evaluation prompt
    const prompt = `Please evaluate this job posting and provide a comprehensive analysis:

Job Title: ${job.title}
Company: ${job.company}
Location: ${job.location}
Compensation: ${job.pay}
Description: ${job.description}
Requirements: ${job.requirements.join(', ')}
Qualifications: ${job.qualifications.join(', ')}
Tags: ${job.tags.join(', ')}

Please analyze this job and provide:
1. A score from 0-100 (where 100 is excellent)
2. Key strengths of this position
3. Potential concerns or drawbacks
4. A brief summary of the role
5. Recommendations for candidates
6. Overall fit level (excellent/good/fair/poor)

Return your response as a JSON object with this structure:
{
  "score": number,
  "strengths": ["strength1", "strength2", ...],
  "concerns": ["concern1", "concern2", ...],
  "summary": "brief description",
  "recommendations": ["rec1", "rec2", ...],
  "fit_level": "excellent|good|fair|poor"
}

Focus on: job market competitiveness, compensation fairness, growth opportunities, work-life balance indicators, and skill development potential.`;

    const response = await fetch(`${OPENROUTER_BASE_URL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'https://jobbify.app',
        'X-Title': 'Jobbify AI Job Evaluation'
      },
      body: JSON.stringify({
        model: 'featherless/qwerky-72b:free',
        messages: [
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 1000
      })
    });

    if (!response.ok) {
      throw new Error(`AI evaluation request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    const aiResponse = data.choices[0].message.content;

    // Try to parse the JSON response
    let evaluation: AIJobEvaluation;
    try {
      // Remove markdown code blocks if present
      let cleanResponse = aiResponse.replace(/```json\n?/g, '').replace(/```\n?/g, '');
      
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = cleanResponse.match(/\{[\s\S]*?\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : cleanResponse;
      
      const parsed = JSON.parse(jsonString);
      
      // Handle different response formats
      evaluation = {
        score: parsed.score || 75,
        strengths: Array.isArray(parsed.strengths) ? parsed.strengths : ['Competitive position', 'Growth opportunity'],
        concerns: Array.isArray(parsed.concerns) ? parsed.concerns : ['Standard considerations apply'],
        summary: typeof parsed.summary === 'string' ? parsed.summary : 
                 (typeof parsed.summary === 'object' && parsed.summary.text) ? parsed.summary.text :
                 'This position shows potential based on the job description and requirements.',
        recommendations: Array.isArray(parsed.recommendations) ? parsed.recommendations : 
                        Array.isArray(parsed.requirements) ? parsed.requirements : 
                        ['Review job requirements carefully', 'Research company culture'],
        fit_level: ['excellent', 'good', 'fair', 'poor'].includes(parsed.fit_level) ? parsed.fit_level : 
                   (parsed.score >= 85) ? 'excellent' : 
                   (parsed.score >= 70) ? 'good' : 
                   (parsed.score >= 55) ? 'fair' : 'poor'
      };
    } catch (parseError) {
      console.warn('Failed to parse AI response as JSON, creating fallback evaluation');
      // Create a fallback evaluation if parsing fails
      evaluation = {
        score: 75,
        strengths: ['Competitive position', 'Growth opportunity'],
        concerns: ['Evaluation temporarily unavailable'],
        summary: 'This position shows potential based on the job description and requirements.',
        recommendations: ['Review job requirements carefully', 'Research company culture'],
        fit_level: 'good'
      };
    }

    // Validate and sanitize the evaluation
    evaluation.score = Math.max(0, Math.min(100, evaluation.score || 75));
    evaluation.strengths = Array.isArray(evaluation.strengths) ? evaluation.strengths : ['Position shows potential'];
    evaluation.concerns = Array.isArray(evaluation.concerns) ? evaluation.concerns : ['Standard considerations apply'];
    evaluation.summary = evaluation.summary || 'Job evaluation analysis';
    evaluation.recommendations = Array.isArray(evaluation.recommendations) ? evaluation.recommendations : ['Consider applying if interested'];
    evaluation.fit_level = ['excellent', 'good', 'fair', 'poor'].includes(evaluation.fit_level) ? evaluation.fit_level : 'good';

    console.log(`AI evaluation completed for ${job.title}: Score ${evaluation.score}/100`);
    return evaluation;

  } catch (error) {
    console.error('Error evaluating job with AI:', error);
    
    // Return a fallback evaluation on error
    return {
      score: 70,
      strengths: ['Professional opportunity available'],
      concerns: ['AI evaluation temporarily unavailable'],
      summary: 'This position appears to be a standard job opportunity. Manual review recommended.',
      recommendations: ['Review job details carefully', 'Research company background'],
      fit_level: 'good'
    };
  }
};

/**
 * Get a color for the evaluation score
 */
export const getEvaluationScoreColor = (score: number): string => {
  if (score >= 85) return '#4CAF50'; // Green - Excellent
  if (score >= 70) return '#2196F3'; // Blue - Good
  if (score >= 55) return '#FF9800'; // Orange - Fair
  return '#F44336'; // Red - Poor
};

/**
 * Get an icon for the fit level
 */
export const getFitLevelIcon = (fitLevel: string): string => {
  switch (fitLevel) {
    case 'excellent': return '🌟';
    case 'good': return '👍';
    case 'fair': return '⚖️';
    case 'poor': return '⚠️';
    default: return '📊';
  }
};

/**
 * Batch evaluate multiple jobs (with rate limiting)
 */
export const batchEvaluateJobs = async (jobs: Job[], userProfile?: any): Promise<Map<string, AIJobEvaluation>> => {
  const evaluations = new Map<string, AIJobEvaluation>();
  
  // Process jobs in batches to avoid rate limiting
  const batchSize = 3;
  const delay = 2000; // 2 seconds between batches
  
  for (let i = 0; i < jobs.length; i += batchSize) {
    const batch = jobs.slice(i, i + batchSize);
    
    // Process batch in parallel
    const batchPromises = batch.map(async (job) => {
      try {
        const evaluation = await evaluateJobWithAI(job, userProfile);
        evaluations.set(job.id, evaluation);
        return { jobId: job.id, evaluation };
      } catch (error) {
        console.error(`Failed to evaluate job ${job.id}:`, error);
        return null;
      }
    });
    
    await Promise.all(batchPromises);
    
    // Add delay between batches (except for the last batch)
    if (i + batchSize < jobs.length) {
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  return evaluations;
};
