import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Alert,
  SafeAreaView,
  Switch
} from 'react-native';
import { router } from 'expo-router';
import { useAppContext } from '@/context/AppContext';
import { LightTheme, DarkTheme } from '@/constants/Theme';
import {
  saveUserJobPreferences,
  getDefaultUserPreferences,
  UserJobPreferences
} from '@/services/jobRecommendationService';
import { markOnboardingComplete } from '@/services/onboardingService';

export default function JobPreferencesOnboarding() {
  const { theme, user } = useAppContext();
  const themeColors = theme === 'light' ? LightTheme : DarkTheme;
  
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5;
  
  // Initialize with default preferences
  const [preferences, setPreferences] = useState<UserJobPreferences>(
    getDefaultUserPreferences(user?.id || '')
  );

  // Step 1: Location Preferences
  const [locationInput, setLocationInput] = useState('');
  const [commuteDistance, setCommuteDistance] = useState('50');

  // Step 2: Job Type Preferences
  const jobTypes = ['Full-time', 'Part-time', 'Contract', 'Freelance', 'Internship'];
  const remoteOptions = [
    { key: 'required', label: 'Remote Only' },
    { key: 'preferred', label: 'Remote Preferred' },
    { key: 'acceptable', label: 'Remote OK' },
    { key: 'not_preferred', label: 'In-Person Preferred' }
  ];

  // Step 3: Role and Industry Preferences
  const [roleInput, setRoleInput] = useState('');
  const [industryInput, setIndustryInput] = useState('');
  const experienceLevels = [
    { key: 'entry', label: 'Entry Level (0-2 years)' },
    { key: 'junior', label: 'Junior (2-4 years)' },
    { key: 'mid', label: 'Mid Level (4-7 years)' },
    { key: 'senior', label: 'Senior (7+ years)' },
    { key: 'lead', label: 'Lead/Manager' },
    { key: 'executive', label: 'Executive' }
  ];

  // Step 4: Salary Preferences
  const [minSalary, setMinSalary] = useState('');
  const [maxSalary, setMaxSalary] = useState('');

  // Step 5: Preference Weights
  const [weights, setWeights] = useState({
    location: 25,
    salary: 30,
    role: 25,
    company: 20
  });

  const addLocation = () => {
    if (locationInput.trim()) {
      setPreferences(prev => ({
        ...prev,
        preferred_locations: [...prev.preferred_locations, locationInput.trim()]
      }));
      setLocationInput('');
    }
  };

  const removeLocation = (index: number) => {
    setPreferences(prev => ({
      ...prev,
      preferred_locations: prev.preferred_locations.filter((_, i) => i !== index)
    }));
  };

  const addRole = () => {
    if (roleInput.trim()) {
      setPreferences(prev => ({
        ...prev,
        preferred_roles: [...prev.preferred_roles, roleInput.trim()]
      }));
      setRoleInput('');
    }
  };

  const removeRole = (index: number) => {
    setPreferences(prev => ({
      ...prev,
      preferred_roles: prev.preferred_roles.filter((_, i) => i !== index)
    }));
  };

  const addIndustry = () => {
    if (industryInput.trim()) {
      setPreferences(prev => ({
        ...prev,
        preferred_industries: [...prev.preferred_industries, industryInput.trim()]
      }));
      setIndustryInput('');
    }
  };

  const removeIndustry = (index: number) => {
    setPreferences(prev => ({
      ...prev,
      preferred_industries: prev.preferred_industries.filter((_, i) => i !== index)
    }));
  };

  const toggleJobType = (jobType: string) => {
    setPreferences(prev => ({
      ...prev,
      preferred_job_types: prev.preferred_job_types.includes(jobType)
        ? prev.preferred_job_types.filter(type => type !== jobType)
        : [...prev.preferred_job_types, jobType]
    }));
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    } else {
      handleSavePreferences();
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSavePreferences = async () => {
    if (!user?.id) {
      setError('User not found. Please log in again.');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Update preferences with current form data
      const finalPreferences: UserJobPreferences = {
        ...preferences,
        max_commute_distance: parseInt(commuteDistance) || 50,
        min_salary: minSalary ? parseInt(minSalary.replace(/[,$]/g, '')) : undefined,
        max_salary: maxSalary ? parseInt(maxSalary.replace(/[,$]/g, '')) : undefined,
        location_weight: weights.location / 100,
        salary_weight: weights.salary / 100,
        role_weight: weights.role / 100,
        company_weight: weights.company / 100
      };

      const success = await saveUserJobPreferences(finalPreferences);

      if (success) {
        // Mark onboarding as complete
        await markOnboardingComplete(user.id);

        Alert.alert(
          'Setup Complete!',
          'Your job preferences have been saved. You\'ll now see personalized job recommendations tailored to your preferences.',
          [
            {
              text: 'Start Browsing Jobs',
              onPress: () => router.replace('/(tabs)/index')
            }
          ]
        );
      } else {
        setError('Failed to save preferences. Please try again.');
      }
    } catch (error) {
      console.error('Error saving job preferences:', error);
      setError('An error occurred while saving your preferences.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepIndicator = () => (
    <View style={styles.stepIndicator}>
      {Array.from({ length: totalSteps }, (_, i) => (
        <View
          key={i}
          style={[
            styles.stepDot,
            {
              backgroundColor: i + 1 <= currentStep ? themeColors.tint : themeColors.border
            }
          ]}
        />
      ))}
    </View>
  );

  const renderLocationStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: themeColors.text }]}>
        Where would you like to work?
      </Text>
      <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
        Add your preferred work locations
      </Text>

      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.textInput, { 
            backgroundColor: themeColors.card,
            color: themeColors.text,
            borderColor: themeColors.border
          }]}
          placeholder="Enter city or state"
          placeholderTextColor={themeColors.textSecondary}
          value={locationInput}
          onChangeText={setLocationInput}
          onSubmitEditing={addLocation}
        />
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: themeColors.tint }]}
          onPress={addLocation}
        >
          <Text style={styles.addButtonText}>Add</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tagContainer}>
        {preferences.preferred_locations.map((location, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.tag, { backgroundColor: themeColors.tint }]}
            onPress={() => removeLocation(index)}
          >
            <Text style={styles.tagText}>{location} ×</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
        Remote Work Preference
      </Text>
      {remoteOptions.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.optionButton,
            {
              backgroundColor: preferences.remote_work_preference === option.key 
                ? themeColors.tint 
                : themeColors.card,
              borderColor: themeColors.border
            }
          ]}
          onPress={() => setPreferences(prev => ({ 
            ...prev, 
            remote_work_preference: option.key as any 
          }))}
        >
          <Text style={[
            styles.optionText,
            {
              color: preferences.remote_work_preference === option.key 
                ? '#FFFFFF' 
                : themeColors.text
            }
          ]}>
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}

      <View style={styles.inputRow}>
        <Text style={[styles.inputLabel, { color: themeColors.text }]}>
          Max Commute Distance (miles)
        </Text>
        <TextInput
          style={[styles.smallInput, { 
            backgroundColor: themeColors.card,
            color: themeColors.text,
            borderColor: themeColors.border
          }]}
          value={commuteDistance}
          onChangeText={setCommuteDistance}
          keyboardType="numeric"
          placeholder="50"
          placeholderTextColor={themeColors.textSecondary}
        />
      </View>

      <View style={styles.switchRow}>
        <Text style={[styles.switchLabel, { color: themeColors.text }]}>
          Willing to relocate
        </Text>
        <Switch
          value={preferences.willing_to_relocate}
          onValueChange={(value) => setPreferences(prev => ({ 
            ...prev, 
            willing_to_relocate: value 
          }))}
          trackColor={{ false: themeColors.border, true: themeColors.tint }}
        />
      </View>
    </View>
  );

  const renderJobTypeStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: themeColors.text }]}>
        What type of work are you looking for?
      </Text>
      <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
        Select all job types you're interested in
      </Text>

      <View style={styles.optionsGrid}>
        {jobTypes.map((jobType) => (
          <TouchableOpacity
            key={jobType}
            style={[
              styles.gridOption,
              {
                backgroundColor: preferences.preferred_job_types.includes(jobType)
                  ? themeColors.tint 
                  : themeColors.card,
                borderColor: themeColors.border
              }
            ]}
            onPress={() => toggleJobType(jobType)}
          >
            <Text style={[
              styles.gridOptionText,
              {
                color: preferences.preferred_job_types.includes(jobType)
                  ? '#FFFFFF' 
                  : themeColors.text
              }
            ]}>
              {jobType}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderRoleStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: themeColors.text }]}>
        What roles are you interested in?
      </Text>
      <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
        Add your preferred job titles and industries
      </Text>

      <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
        Experience Level
      </Text>
      {experienceLevels.map((level) => (
        <TouchableOpacity
          key={level.key}
          style={[
            styles.optionButton,
            {
              backgroundColor: preferences.experience_level === level.key
                ? themeColors.tint
                : themeColors.card,
              borderColor: themeColors.border
            }
          ]}
          onPress={() => setPreferences(prev => ({
            ...prev,
            experience_level: level.key as any
          }))}
        >
          <Text style={[
            styles.optionText,
            {
              color: preferences.experience_level === level.key
                ? '#FFFFFF'
                : themeColors.text
            }
          ]}>
            {level.label}
          </Text>
        </TouchableOpacity>
      ))}

      <Text style={[styles.sectionLabel, { color: themeColors.text, marginTop: 20 }]}>
        Preferred Roles
      </Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.textInput, {
            backgroundColor: themeColors.card,
            color: themeColors.text,
            borderColor: themeColors.border
          }]}
          placeholder="e.g., Software Engineer, Product Manager"
          placeholderTextColor={themeColors.textSecondary}
          value={roleInput}
          onChangeText={setRoleInput}
          onSubmitEditing={addRole}
        />
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: themeColors.tint }]}
          onPress={addRole}
        >
          <Text style={styles.addButtonText}>Add</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tagContainer}>
        {preferences.preferred_roles.map((role, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.tag, { backgroundColor: themeColors.tint }]}
            onPress={() => removeRole(index)}
          >
            <Text style={styles.tagText}>{role} ×</Text>
          </TouchableOpacity>
        ))}
      </View>

      <Text style={[styles.sectionLabel, { color: themeColors.text }]}>
        Preferred Industries
      </Text>
      <View style={styles.inputContainer}>
        <TextInput
          style={[styles.textInput, {
            backgroundColor: themeColors.card,
            color: themeColors.text,
            borderColor: themeColors.border
          }]}
          placeholder="e.g., Technology, Healthcare, Finance"
          placeholderTextColor={themeColors.textSecondary}
          value={industryInput}
          onChangeText={setIndustryInput}
          onSubmitEditing={addIndustry}
        />
        <TouchableOpacity
          style={[styles.addButton, { backgroundColor: themeColors.tint }]}
          onPress={addIndustry}
        >
          <Text style={styles.addButtonText}>Add</Text>
        </TouchableOpacity>
      </View>

      <View style={styles.tagContainer}>
        {preferences.preferred_industries.map((industry, index) => (
          <TouchableOpacity
            key={index}
            style={[styles.tag, { backgroundColor: themeColors.tint }]}
            onPress={() => removeIndustry(index)}
          >
            <Text style={styles.tagText}>{industry} ×</Text>
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  const renderSalaryStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: themeColors.text }]}>
        What's your salary expectation?
      </Text>
      <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
        Set your desired salary range (optional)
      </Text>

      <View style={styles.salaryContainer}>
        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Minimum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 80000"
            placeholderTextColor={themeColors.textSecondary}
            value={minSalary}
            onChangeText={setMinSalary}
            keyboardType="numeric"
          />
        </View>

        <View style={styles.salaryInputContainer}>
          <Text style={[styles.inputLabel, { color: themeColors.text }]}>
            Maximum Salary
          </Text>
          <TextInput
            style={[styles.textInput, {
              backgroundColor: themeColors.card,
              color: themeColors.text,
              borderColor: themeColors.border
            }]}
            placeholder="e.g., 120000"
            placeholderTextColor={themeColors.textSecondary}
            value={maxSalary}
            onChangeText={setMaxSalary}
            keyboardType="numeric"
          />
        </View>
      </View>

      <View style={styles.switchRow}>
        <Text style={[styles.switchLabel, { color: themeColors.text }]}>
          Salary is negotiable
        </Text>
        <Switch
          value={preferences.salary_negotiable}
          onValueChange={(value) => setPreferences(prev => ({
            ...prev,
            salary_negotiable: value
          }))}
          trackColor={{ false: themeColors.border, true: themeColors.tint }}
        />
      </View>
    </View>
  );

  const renderWeightsStep = () => (
    <View style={styles.stepContainer}>
      <Text style={[styles.stepTitle, { color: themeColors.text }]}>
        What matters most to you?
      </Text>
      <Text style={[styles.stepDescription, { color: themeColors.textSecondary }]}>
        Adjust the importance of different factors in job recommendations
      </Text>

      <View style={styles.weightsContainer}>
        <View style={styles.weightItem}>
          <Text style={[styles.weightLabel, { color: themeColors.text }]}>
            Location: {weights.location}%
          </Text>
          <View style={styles.sliderContainer}>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                location: Math.max(0, prev.location - 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>-</Text>
            </TouchableOpacity>
            <View style={[styles.sliderTrack, { backgroundColor: themeColors.border }]}>
              <View
                style={[
                  styles.sliderFill,
                  {
                    backgroundColor: themeColors.tint,
                    width: `${weights.location}%`
                  }
                ]}
              />
            </View>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                location: Math.min(100, prev.location + 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.weightItem}>
          <Text style={[styles.weightLabel, { color: themeColors.text }]}>
            Salary: {weights.salary}%
          </Text>
          <View style={styles.sliderContainer}>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                salary: Math.max(0, prev.salary - 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>-</Text>
            </TouchableOpacity>
            <View style={[styles.sliderTrack, { backgroundColor: themeColors.border }]}>
              <View
                style={[
                  styles.sliderFill,
                  {
                    backgroundColor: themeColors.tint,
                    width: `${weights.salary}%`
                  }
                ]}
              />
            </View>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                salary: Math.min(100, prev.salary + 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.weightItem}>
          <Text style={[styles.weightLabel, { color: themeColors.text }]}>
            Role Match: {weights.role}%
          </Text>
          <View style={styles.sliderContainer}>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                role: Math.max(0, prev.role - 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>-</Text>
            </TouchableOpacity>
            <View style={[styles.sliderTrack, { backgroundColor: themeColors.border }]}>
              <View
                style={[
                  styles.sliderFill,
                  {
                    backgroundColor: themeColors.tint,
                    width: `${weights.role}%`
                  }
                ]}
              />
            </View>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                role: Math.min(100, prev.role + 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>+</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.weightItem}>
          <Text style={[styles.weightLabel, { color: themeColors.text }]}>
            Company: {weights.company}%
          </Text>
          <View style={styles.sliderContainer}>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                company: Math.max(0, prev.company - 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>-</Text>
            </TouchableOpacity>
            <View style={[styles.sliderTrack, { backgroundColor: themeColors.border }]}>
              <View
                style={[
                  styles.sliderFill,
                  {
                    backgroundColor: themeColors.tint,
                    width: `${weights.company}%`
                  }
                ]}
              />
            </View>
            <TouchableOpacity
              style={[styles.sliderButton, { backgroundColor: themeColors.border }]}
              onPress={() => setWeights(prev => ({
                ...prev,
                company: Math.min(100, prev.company + 5)
              }))}
            >
              <Text style={[styles.sliderButtonText, { color: themeColors.text }]}>+</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>

      <Text style={[styles.totalText, { color: themeColors.textSecondary }]}>
        Total: {weights.location + weights.salary + weights.role + weights.company}%
      </Text>
    </View>
  );

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return renderLocationStep();
      case 2:
        return renderJobTypeStep();
      case 3:
        return renderRoleStep();
      case 4:
        return renderSalaryStep();
      case 5:
        return renderWeightsStep();
      default:
        return <Text>Step {currentStep}</Text>;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: themeColors.background }]}>
      {renderStepIndicator()}
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {renderStep()}
        
        {error ? (
          <Text style={[styles.errorText, { color: themeColors.error }]}>
            {error}
          </Text>
        ) : null}
      </ScrollView>

      <View style={[styles.buttonContainer, { backgroundColor: themeColors.background }]}>
        {currentStep > 1 && (
          <TouchableOpacity
            style={[styles.secondaryButton, { borderColor: themeColors.border }]}
            onPress={prevStep}
          >
            <Text style={[styles.secondaryButtonText, { color: themeColors.text }]}>
              Previous
            </Text>
          </TouchableOpacity>
        )}
        
        <TouchableOpacity
          style={[
            styles.primaryButton,
            { backgroundColor: themeColors.tint },
            loading && styles.disabledButton
          ]}
          onPress={nextStep}
          disabled={loading}
        >
          <Text style={styles.primaryButtonText}>
            {loading ? 'Saving...' : currentStep === totalSteps ? 'Complete Setup' : 'Next'}
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  stepIndicator: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    gap: 8,
  },
  stepDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 20,
  },
  stepContainer: {
    paddingBottom: 40,
  },
  stepTitle: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 8,
    textAlign: 'center',
  },
  stepDescription: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
  },
  inputContainer: {
    flexDirection: 'row',
    marginBottom: 20,
    gap: 10,
  },
  textInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  addButton: {
    height: 50,
    paddingHorizontal: 20,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  addButtonText: {
    color: '#FFFFFF',
    fontWeight: '600',
    fontSize: 16,
  },
  tagContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 30,
  },
  tag: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  tagText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
  sectionLabel: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  optionButton: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 10,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  inputRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 15,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  smallInput: {
    width: 80,
    height: 40,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 12,
    textAlign: 'center',
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginVertical: 15,
  },
  switchLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  gridOption: {
    flex: 1,
    minWidth: '45%',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  gridOptionText: {
    fontSize: 16,
    fontWeight: '500',
  },
  buttonContainer: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  primaryButton: {
    flex: 1,
    height: 50,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  primaryButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    flex: 1,
    height: 50,
    borderRadius: 12,
    borderWidth: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  secondaryButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  disabledButton: {
    opacity: 0.6,
  },
  errorText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 10,
  },
  salaryContainer: {
    gap: 20,
    marginBottom: 20,
  },
  salaryInputContainer: {
    gap: 8,
  },
  weightsContainer: {
    gap: 20,
    marginBottom: 20,
  },
  weightItem: {
    gap: 10,
  },
  weightLabel: {
    fontSize: 16,
    fontWeight: '600',
  },
  sliderContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  sliderButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sliderButtonText: {
    fontSize: 18,
    fontWeight: '600',
  },
  sliderTrack: {
    flex: 1,
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  sliderFill: {
    height: '100%',
    borderRadius: 4,
  },
  totalText: {
    fontSize: 14,
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
