import { Job } from '@/context/AppContext';
import { supabase } from '@/lib/supabase';
import Constants from 'expo-constants';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { processJobDescriptionWithAI, ProcessedJobDescription } from './aiAssistantService';
import { jobEvaluationService } from './jobEvaluationService';
import { fetchJobs as fetchJobsFromLocalAPI } from './remoteOkService';

interface ApiJob {
  job_title: string;
  company_name: string | { display_name?: string; name?: string };
  job_location: string;
  job_description: string;
  job_salary_range?: string;
  job_id: string;
  job_posted_date?: string;
  job_apply_link?: string;
  job_category?: string;
  job_type?: string;
  job_employment_type?: string;
}

// RemoteOK API (prioritized)
const REMOTEOK_API_URL = 'https://remoteok.io/api';

// Fallback RapidAPI
const API_HOST = 'active-jobs-db.p.rapidapi.com';
const API_KEY = '**************************************************';
const API_URL = 'https://active-jobs-db.p.rapidapi.com/active-ats-expired';

// Note: Ashby integration removed - focusing on RemoteOK API, external APIs and database jobs

/**
 * Fetch jobs from RemoteOK API (prioritized source)
 */
const fetchJobsFromRemoteOK = async (): Promise<Job[]> => {
  try {
    console.log('Fetching jobs from RemoteOK API...');

    const response = await fetch(REMOTEOK_API_URL, {
      headers: {
        'User-Agent': 'Jobbify-App/1.0',
        'Accept': 'application/json',
      },
    });

    if (!response.ok) {
      console.error(`RemoteOK API failed with status ${response.status}`);
      return [];
    }

    const data = await response.json();

    // RemoteOK returns an array where first item is metadata, rest are jobs
    const jobs = data.slice(1);

    if (!jobs || jobs.length === 0) {
      console.log('No jobs returned from RemoteOK API');
      return [];
    }

    console.log(`Fetched ${jobs.length} jobs from RemoteOK`);

    // Filter for recent jobs (last 30 days) and process with AI
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentJobs = jobs.filter((job: any) => {
      if (!job.date) return true; // Include jobs without date
      const jobDate = new Date(job.date);
      return jobDate >= thirtyDaysAgo;
    });

    console.log(`Filtered to ${recentJobs.length} recent jobs from RemoteOK`);

    // Process jobs with AI enhancement
    const processedJobs = await Promise.all(
      recentJobs.slice(0, 20).map(async (remoteJob: any) => { // Limit to 20 jobs for performance
        try {
          // Use company logo if available, otherwise generate Clearbit URL
          let companyLogo = '';
          if (remoteJob.company_logo && remoteJob.company_logo.trim() !== '') {
            companyLogo = remoteJob.company_logo;
          } else {
            const companyName = remoteJob.company.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
            companyLogo = `https://logo.clearbit.com/${companyName}.com?size=300`;
          }

          // Format salary
          let salary = 'Competitive salary';
          if (remoteJob.salary) {
            salary = remoteJob.salary;
          } else if (remoteJob.salary_min && remoteJob.salary_max) {
            salary = `$${remoteJob.salary_min.toLocaleString()} - $${remoteJob.salary_max.toLocaleString()}`;
          } else if (remoteJob.salary_min) {
            salary = `$${remoteJob.salary_min.toLocaleString()}+`;
          }

          // Process description with AI
          let processedDescription;
          try {
            processedDescription = await processJobDescriptionWithAI(
              remoteJob.description || '',
              remoteJob.position,
              remoteJob.company
            );
          } catch (error) {
            console.warn(`AI processing failed for RemoteOK job ${remoteJob.position}, using fallback`);
            processedDescription = {
              cleanDescription: cleanDescriptionManually(remoteJob.description || ''),
              qualifications: extractQualificationsFromTags(remoteJob.tags || [], remoteJob.position),
              requirements: extractRequirementsFromDescription(remoteJob.description || '', remoteJob.position),
              keyHighlights: [`Remote ${remoteJob.position} at ${remoteJob.company}`, 'Fully remote work', 'Competitive compensation'],
              summary: `${remoteJob.company} is seeking a ${remoteJob.position} for a fully remote position.`,
              aiExplanation: `Let me explain this ${remoteJob.position} role at ${remoteJob.company}. This is a remote position that allows you to work from anywhere. Based on the job description, you would be responsible for typical ${remoteJob.position} duties. The company appears to value skills related to ${(remoteJob.tags || []).slice(0, 3).join(', ')}. If you have experience in these areas and enjoy working remotely with flexibility, this position might be worth exploring. Consider how your background aligns with what they're looking for, and think about specific examples from your experience that demonstrate these abilities.`
            };
          }

          return {
            id: `remoteok-${remoteJob.id}`,
            title: remoteJob.position,
            company: remoteJob.company,
            location: remoteJob.location || 'Remote',
            pay: salary,
            image: companyLogo,
            logo: companyLogo,
            distance: 'Remote',
            tags: [...(remoteJob.tags || []).slice(0, 5), 'Remote'], // Include top tags + Remote
            description: processedDescription.cleanDescription,
            qualifications: processedDescription.qualifications,
            requirements: processedDescription.requirements,
            aiExplanation: processedDescription.aiExplanation,
            url: remoteJob.apply_url || remoteJob.url || `https://remoteok.io/remote-jobs/${remoteJob.slug}`,
            postedDate: remoteJob.date ? new Date(remoteJob.date).toISOString() : new Date().toISOString()
          };
        } catch (error) {
          console.error(`Error processing RemoteOK job ${remoteJob.position}:`, error);
          return null;
        }
      })
    );

    // Filter out null results and prioritize by logo availability
    const validJobs = processedJobs.filter((job): job is Job => job !== null);
    const prioritizedJobs = await prioritizeJobsWithLogos(validJobs);

    console.log(`Successfully processed ${prioritizedJobs.length} jobs from RemoteOK`);
    return prioritizedJobs;

  } catch (error) {
    console.error('Error fetching jobs from RemoteOK:', error);
    return [];
  }
};

/**
 * Fetch real jobs from the Supabase database
 */
const fetchJobsFromDatabase = async (): Promise<Job[]> => {
  try {
    console.log('Fetching jobs from Supabase database...');

    const { data: jobs, error } = await supabase
      .from('jobs')
      .select('*')
      .limit(50)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching jobs from database:', error);
      return [];
    }

    if (!jobs || jobs.length === 0) {
      console.log('No jobs found in database');
      return [];
    }

    // Convert database jobs to our Job interface with proper logo URLs
    const convertedJobs: Job[] = jobs.map(job => {
      // Generate proper logo URL for database jobs
      const companyName = (job.company || 'Unknown Company').toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
      const companyLogo = `https://logo.clearbit.com/${companyName}.com?size=300`;

      return {
        id: job.id, // Use the real UUID from database
        title: job.title || 'Unknown Job',
        company: job.company || 'Unknown Company',
        location: job.location || 'Remote',
        pay: job.salary || 'Competitive',
        image: job.logo || companyLogo,
        logo: job.logo || companyLogo,
        distance: '2 km', // Default distance
        tags: job.requirements || ['Remote', 'Full-time'],
        description: job.description || 'No description available',
        qualifications: job.qualifications || ['Experience required'],
        requirements: job.requirements || ['Bachelor\'s degree preferred'],
        aiExplanation: job.ai_explanation || `Let me explain this ${job.title || 'Unknown Job'} role at ${job.company || 'Unknown Company'}. Based on the job description, this position involves responsibilities typical for this role. The company seems to value skills related to the requirements they've listed. If you have experience in these areas and are looking for a role where you can apply these skills, this position might be worth exploring. Consider how your background aligns with what they're looking for.`,
        url: job.url || '',
        postedDate: job.created_at
      };
    });

    console.log(`Successfully converted ${convertedJobs.length} jobs from database`);

    // Prioritize database jobs with original logos
    const prioritizedJobs = await prioritizeJobsWithLogos(convertedJobs);
    return prioritizedJobs;
  } catch (error) {
    console.error('Exception fetching jobs from database:', error);
    return [];
  }
};

// No mock jobs - we'll only use the local API

/**
 * Fetch jobs exclusively from the local API
 * No fallbacks to other sources - only use the FastAPI backend
 */
export const fetchJobs = async (page = 1, limit = 20): Promise<Job[]> => {
  try {
    console.log('Fetching jobs from local API only...');

    // Local API (FastAPI backend)
    const localApiJobs = await fetchJobsFromLocalAPI();
    console.log(`Retrieved ${localApiJobs.length} jobs from local API`);
    return localApiJobs;
  } catch (error) {
    console.error('Error fetching jobs from local API:', error);
    return [];
  }
};

/**
 * Filter mock data to only show jobs from the past week and prioritize by logo availability
 */
const filterMostRecentJobsFromMockData = async (jobs: Job[]): Promise<Job[]> => {
  // Calculate date from one week ago
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

  // Generate random recent dates for mock data
  // In real data, we'd use the actual job posting date
  const jobsWithDates = jobs.map(job => {
    // Create a random date between now and a week ago
    const daysAgo = Math.floor(Math.random() * 7);
    const randomRecentDate = new Date();
    randomRecentDate.setDate(randomRecentDate.getDate() - daysAgo);

    // Add posting date to the job
    return {
      ...job,
      postedDate: randomRecentDate.toISOString()
    };
  });

  // Prioritize mock jobs with logos (though mock jobs should have logos)
  const prioritizedJobs = await prioritizeJobsWithLogos(jobsWithDates);
  return prioritizedJobs;
};

/**
 * Map API response to our app's Job interface with AI-enhanced processing
 * Strictly filters jobs to meet ALL criteria:
 * 1. Has picture/logo, job URL, description, pay range, qualifications, requirements
 * 2. Posted within the last week
 * 3. Uses AI to process job descriptions for better quality and readability
 */
const mapApiJobsToAppJobs = async (apiResponse: any): Promise<Job[]> => {
  if (!apiResponse || !apiResponse.data || !Array.isArray(apiResponse.data)) {
    return [];
  }
  
  // Calculate date from one week ago
  const oneWeekAgo = new Date();
  oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
  
  // Filter out jobs without essential fields (less strict to preserve more jobs)
  const validJobs = apiResponse.data.filter((apiJob: ApiJob) => {
    // Essential: Must have title and company
    const hasTitle = apiJob.job_title && apiJob.job_title.trim().length > 0;
    const companyNameStr = typeof apiJob.company_name === 'string'
      ? apiJob.company_name
      : (apiJob.company_name?.display_name || apiJob.company_name?.name || '');
    const hasCompany = companyNameStr.trim().length > 0;

    // Essential: Must have substantial description (minimum 2500 chars for quality content)
    const hasDescription = apiJob.job_description && apiJob.job_description.trim().length >= 2500;

    // Optional: Job URL (we'll provide fallback if missing)
    const hasJobUrl = apiJob.job_apply_link && apiJob.job_apply_link.trim().length > 0;

    // Optional: Pay range (we'll provide fallback if missing)
    const hasPayRange = apiJob.job_salary_range && apiJob.job_salary_range.trim().length > 0;

    // Optional: Posting date (we'll use current date if missing)
    let isRecent = true; // Default to true to be less strict
    if (apiJob.job_posted_date) {
      const postDate = new Date(apiJob.job_posted_date);
      isRecent = !isNaN(postDate.getTime()) && postDate >= oneWeekAgo;
    }

    // We can always generate a fallback image from company name if we have a company
    const canGenerateImage = hasCompany;
    
    // Job must meet essential criteria: title, company, description, and ability to generate image
    return hasTitle && hasCompany && hasDescription && canGenerateImage;
  });
  
  // Process jobs with AI enhancement and robust fallbacks
  const processedJobs = await Promise.all(validJobs.map(async (apiJob: ApiJob) => {
    // Extract salary range with better fallbacks
    let pay = 'Competitive salary';
    if (apiJob.job_salary_range && apiJob.job_salary_range.trim().length > 0) {
      pay = apiJob.job_salary_range;
    } else {
      // Try to extract salary from description
      const description = apiJob.job_description || '';
      const salaryMatch = description.match(/\$[\d,]+(?:\s*-\s*\$[\d,]+)?(?:\s*(?:per|\/)\s*(?:year|hour|month))?/i);
      if (salaryMatch) {
        pay = salaryMatch[0];
      }
    }

    const companyNameForAI = typeof apiJob.company_name === 'string'
        ? apiJob.company_name
        : (apiJob.company_name?.display_name || apiJob.company_name?.name || 'Unknown Company');

    // Generate smarter tags based on job title and description
    const possibleTags = ['Remote', 'Full-time', 'Part-time', 'Contract', 'JavaScript', 'React',
      'Python', 'UI/UX', 'Data Science', 'Marketing', 'Sales', 'Engineering', 'Design'];
    const randomTags: string[] = [];

    // Add job-specific tags based on title
    const jobTitle = apiJob.job_title.toLowerCase();
    if (jobTitle.includes('remote')) randomTags.push('Remote');
    if (jobTitle.includes('full') || jobTitle.includes('time')) randomTags.push('Full-time');
    if (jobTitle.includes('part')) randomTags.push('Part-time');
    if (jobTitle.includes('contract')) randomTags.push('Contract');

    // Add technology tags based on title/description
    const content = (apiJob.job_title + ' ' + apiJob.job_description).toLowerCase();
    if (content.includes('javascript') || content.includes('js')) randomTags.push('JavaScript');
    if (content.includes('react')) randomTags.push('React');
    if (content.includes('python')) randomTags.push('Python');
    if (content.includes('design') || content.includes('ui') || content.includes('ux')) randomTags.push('UI/UX');

    // Add random tags if we don't have enough
    while (randomTags.length < 3) {
      const randomTag = possibleTags[Math.floor(Math.random() * possibleTags.length)];
      if (!randomTags.includes(randomTag)) {
        randomTags.push(randomTag);
      }
    }

    // Add type/category as tag if available
    if (apiJob.job_category) randomTags.push(apiJob.job_category);
    if (apiJob.job_type) randomTags.push(apiJob.job_type);

    // Random distance for UI display
    const distance = `${Math.floor(Math.random() * 20) + 1} km`;

    // Extract company name properly for logo generation
    const rawCompanyName = companyNameForAI;

    // Generate company logo URL using Clearbit API
    const cleanCompanyName = rawCompanyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
    const companyLogo = `https://logo.clearbit.com/${cleanCompanyName}.com?size=300`;

    // Fallback images for when Clearbit doesn't have the logo
    const fallbackImages = [
      'https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80',
      'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=688&q=80',
      'https://images.unsplash.com/photo-1573496799652-1c28c88b4f3e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1169&q=80',
      'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
      'https://images.unsplash.com/photo-1573497161161-c3e73707e25c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=687&q=80',
    ];
    const fallbackImage = fallbackImages[Math.floor(Math.random() * fallbackImages.length)];

    // Process job description with AI for better quality and readability

    console.log(`Processing job: ${apiJob.job_title} at ${companyNameForAI} with AI...`);

    let processedDescription;
    let jobEvaluation;
    
    try {
      // Process description with AI
      processedDescription = await processJobDescriptionWithAI(
        apiJob.job_description || '',
        apiJob.job_title,
        companyNameForAI
      );
      
      // Get AI evaluation of the job
      console.log(`Getting AI evaluation for: ${apiJob.job_title} at ${companyNameForAI}`);
      jobEvaluation = await jobEvaluationService.evaluateJob(
        apiJob.job_title,
        companyNameForAI,
        apiJob.job_description || '',
        apiJob.job_location || ''
      );
    } catch (error) {
      console.warn(`AI processing failed for job ${apiJob.job_title}, using fallback:`, error);
      // Fallback to manual processing if AI fails
      processedDescription = {
        cleanDescription: cleanDescriptionManually(apiJob.job_description || ''),
        qualifications: extractQualificationsManually(apiJob.job_description || '', apiJob.job_title),
        requirements: extractRequirementsManually(apiJob.job_description || '', apiJob.job_title),
        keyHighlights: [`Work as ${apiJob.job_title} at ${companyNameForAI}`, 'Competitive compensation', 'Growth opportunities'],
        summary: `${companyNameForAI} is seeking a ${apiJob.job_title} to join their team.`
      };
      
      // Fallback evaluation
      jobEvaluation = await jobEvaluationService.evaluateJob(
        apiJob.job_title,
        companyNameForAI,
        processedDescription.cleanDescription,
        apiJob.job_location || ''
      );
    }
    
    // Use the already extracted company name from logo generation
    const companyName = rawCompanyName;

    // Combine the original description with AI evaluation
    const evaluationDisplay = jobEvaluationService.formatEvaluationForDisplay(jobEvaluation);
    const enhancedDescription = `${processedDescription.cleanDescription}

---

${evaluationDisplay}`;

    return {
      id: apiJob.job_id,
      title: apiJob.job_title,
      company: companyName, // Use extracted string company name
      location: apiJob.job_location || 'Location not specified',
      pay,
      image: companyLogo, // Use company logo as primary image
      logo: companyLogo, // Use company logo
      distance,
      tags: [...new Set(randomTags)], // Remove any duplicates
      description: enhancedDescription, // Include AI evaluation with description
      qualifications: processedDescription.qualifications, // Use AI-extracted qualifications
      requirements: processedDescription.requirements, // Use AI-extracted requirements
      url: apiJob.job_apply_link || '#', // Provide fallback URL
      postedDate: apiJob.job_posted_date ? new Date(apiJob.job_posted_date).toISOString() : new Date().toISOString(),
      aiEvaluation: jobEvaluation // Store evaluation separately for potential future use
    };
  }));

  // Sort jobs to prioritize those with original logos
  const sortedJobs = await prioritizeJobsWithLogos(processedJobs);
  return sortedJobs;
};

// Well-known companies that definitely have logos on Clearbit
const COMPANIES_WITH_LOGOS = new Set([
  // Big Tech
  'google', 'microsoft', 'apple', 'amazon', 'facebook', 'meta', 'netflix', 'uber', 'airbnb',
  'spotify', 'twitter', 'linkedin', 'github', 'slack', 'zoom', 'salesforce', 'adobe',
  'intel', 'nvidia', 'tesla', 'paypal', 'stripe', 'shopify', 'dropbox', 'atlassian',
  'oracle', 'ibm', 'cisco', 'vmware', 'mongodb', 'redis', 'docker', 'kubernetes',
  
  // Additional well-known companies
  'coinbase', 'twilio', 'square', 'robinhood', 'pinterest', 'snapchat', 'tiktok', 'discord',
  'figma', 'notion', 'airtable', 'canva', 'calendly', 'hubspot', 'zendesk', 'intercom',
  'mailchimp', 'sendgrid', 'twitch', 'reddit', 'medium', 'substack', 'vercel', 'netlify',
  'heroku', 'digitalocean', 'linode', 'cloudflare', 'fastly', 'auth0', 'okta', 'databricks',
  'snowflake', 'palantir', 'unity', 'epic', 'roblox', 'activision', 'ea', 'take2',
  'shopee', 'grab', 'gojek', 'sea', 'bytedance', 'tencent', 'alibaba', 'baidu',
  
  // Our mock companies
  'techcorp', 'designhub', 'dataflow', 'appworks', 'cloudnine'
]);

// Function to check if a company logo exists via Clearbit API
async function checkLogoExists(logoUrl: string | undefined, companyName: string): Promise<boolean> {
  try {
    if (!logoUrl) {
      return false;
    }
    
    // Check if it's a generated/placeholder logo (ui-avatars.com, etc.)
    if (logoUrl.includes('ui-avatars.com') || 
        logoUrl.includes('placeholder') || 
        logoUrl.includes('avatar') ||
        logoUrl.includes('unsplash.com')) {
      return false; // These are generated/stock images, not original logos
    }
    
    // Quick check for well-known companies
    const cleanCompanyName = companyName.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, '');
    if (COMPANIES_WITH_LOGOS.has(cleanCompanyName)) {
      return true;
    }
    
    // Check for Clearbit logo URLs (these are original company logos)
    if (logoUrl.includes('logo.clearbit.com')) {
      try {
        const response = await fetch(logoUrl, { method: 'HEAD' });
        return response.ok && response.status === 200;
      } catch {
        return false;
      }
    }

    // For other URLs, do a quick HEAD request
    const response = await fetch(logoUrl, { method: 'HEAD' });
    return response.ok && response.status === 200;
  } catch (error) {
    return false;
  }
}

// Function to prioritize jobs with original logos
async function prioritizeJobsWithLogos(jobs: Job[]): Promise<Job[]> {
  console.log('Checking logo availability for job prioritization...');

  // Check logo availability for each job (with timeout to avoid delays)
  const jobsWithLogoStatus = await Promise.all(
    jobs.map(async (job) => {
      try {
        // Set a timeout for logo checking to avoid long delays
        const logoCheckPromise = checkLogoExists(job.logo, job.company);
        const timeoutPromise = new Promise<boolean>((resolve) =>
          setTimeout(() => resolve(false), 1500) // 1.5 second timeout (reduced for better performance)
        );

        const hasOriginalLogo = await Promise.race([logoCheckPromise, timeoutPromise]);

        // Enhanced scoring system for better prioritization
        let logoScore = 0;
        if (hasOriginalLogo && job.logo) {
          // Higher scores for better logo sources
          if (job.logo.includes('logo.clearbit.com')) {
            logoScore = 3; // Clearbit logos are high quality
          } else if (COMPANIES_WITH_LOGOS.has(job.company.toLowerCase().replace(/\s+/g, '').replace(/[^a-z0-9]/g, ''))) {
            logoScore = 2; // Well-known companies
          } else {
            logoScore = 1; // Other original logos
          }
        }
        
        return {
          ...job,
          hasOriginalLogo,
          logoScore
        };
      } catch (error) {
        console.warn(`Logo check failed for ${job.company}:`, error);
        return {
          ...job,
          hasOriginalLogo: false,
          logoScore: 0
        };
      }
    })
  );

  // Sort jobs: Original logos first, then by posting date
  const sortedJobs = jobsWithLogoStatus.sort((a, b) => {
    // Primary sort: Jobs with original logos first
    if (a.logoScore !== b.logoScore) {
      return b.logoScore - a.logoScore; // Higher score (original logo) first
    }

    // Secondary sort: More recent jobs first
    const dateA = new Date(a.postedDate || 0).getTime();
    const dateB = new Date(b.postedDate || 0).getTime();
    return dateB - dateA;
  });

  // Remove the temporary logoScore property
  const finalJobs = sortedJobs.map(({ logoScore, hasOriginalLogo, ...job }) => job);

  const jobsWithLogos = sortedJobs.filter(job => job.hasOriginalLogo).length;
  console.log(`Prioritized ${jobsWithLogos} jobs with original logos out of ${jobs.length} total jobs`);

  return finalJobs;
}

// Manual processing fallback functions
function cleanDescriptionManually(description: string): string {
  return description
    .replace(/```[\s\S]*?```/g, '') // Remove code blocks
    .replace(/`([^`]+)`/g, '$1') // Remove inline code formatting
    .replace(/\*\*([^*]+)\*\*/g, '$1') // Remove bold markdown
    .replace(/\*([^*]+)\*/g, '$1') // Remove italic markdown
    .replace(/\$\{[^}]*\}/g, '') // Remove template literals
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();
}

function extractQualificationsManually(description: string, jobTitle: string): string[] {
  const qualifications = [];
  const content = description.toLowerCase();

  // Basic skill extraction
  if (content.includes('javascript') || content.includes('js')) qualifications.push('JavaScript experience');
  if (content.includes('react')) qualifications.push('React framework knowledge');
  if (content.includes('python')) qualifications.push('Python programming skills');
  if (content.includes('communication')) qualifications.push('Excellent communication skills');
  if (content.includes('team') || content.includes('collaboration')) qualifications.push('Team collaboration experience');
  if (content.includes('problem') || content.includes('solving')) qualifications.push('Strong problem-solving abilities');

  // Job-specific qualifications
  if (jobTitle.toLowerCase().includes('senior')) qualifications.push('Senior-level experience required');
  if (jobTitle.toLowerCase().includes('developer') || jobTitle.toLowerCase().includes('engineer')) {
    qualifications.push('Software development experience');
    qualifications.push('Technical problem-solving skills');
  }

  // Fallback qualifications
  if (qualifications.length === 0) {
    qualifications.push('Relevant experience in the field');
    qualifications.push('Strong analytical skills');
    qualifications.push('Excellent communication abilities');
  }

  return qualifications.slice(0, 8); // Limit to 8 qualifications
}

function extractRequirementsManually(description: string, jobTitle: string): string[] {
  const requirements = [];
  const content = description.toLowerCase();

  // Basic requirement extraction
  if (content.includes('degree') || content.includes('bachelor') || content.includes('education')) {
    requirements.push('Bachelor\'s degree or equivalent experience');
  }
  if (content.includes('years') && content.includes('experience')) {
    const yearMatch = content.match(/(\d+)\+?\s*years?\s*(?:of\s*)?experience/);
    if (yearMatch) {
      requirements.push(`${yearMatch[1]}+ years of relevant experience`);
    } else {
      requirements.push('Previous relevant experience required');
    }
  }

  // Job-specific requirements
  if (jobTitle.toLowerCase().includes('developer') || jobTitle.toLowerCase().includes('engineer')) {
    requirements.push('Programming experience required');
  }
  if (jobTitle.toLowerCase().includes('senior')) {
    requirements.push('Senior-level experience required');
  }

  // Fallback requirements
  if (requirements.length === 0) {
    requirements.push('Relevant educational background or experience');
    requirements.push('Strong work ethic and dedication');
    requirements.push('Ability to work in a team environment');
  }

  return requirements.slice(0, 6); // Limit to 6 requirements
}

// Helper functions for RemoteOK processing
function extractQualificationsFromTags(tags: string[], jobTitle: string): string[] {
  const qualifications: string[] = [];

  // Convert tags to qualifications
  tags.forEach(tag => {
    const lowerTag = tag.toLowerCase();
    if (lowerTag.includes('javascript') || lowerTag === 'js') {
      qualifications.push('JavaScript programming experience');
    } else if (lowerTag.includes('react')) {
      qualifications.push('React framework knowledge');
    } else if (lowerTag.includes('python')) {
      qualifications.push('Python programming skills');
    } else if (lowerTag.includes('node')) {
      qualifications.push('Node.js development experience');
    } else if (lowerTag.includes('typescript')) {
      qualifications.push('TypeScript experience');
    } else if (lowerTag.length > 2) {
      qualifications.push(`Experience with ${tag}`);
    }
  });

  // Add general qualifications
  qualifications.push('Strong communication skills for remote work');
  qualifications.push('Self-motivated and independent work style');
  qualifications.push('Experience with remote collaboration tools');

  return qualifications.slice(0, 8);
}

function extractRequirementsFromDescription(description: string, jobTitle: string): string[] {
  const requirements: string[] = [];
  const content = description.toLowerCase();

  // Basic requirement extraction
  if (content.includes('degree') || content.includes('bachelor') || content.includes('education')) {
    requirements.push('Bachelor\'s degree or equivalent experience');
  }
  if (content.includes('years') && content.includes('experience')) {
    const yearMatch = content.match(/(\d+)\+?\s*years?\s*(?:of\s*)?experience/);
    if (yearMatch) {
      requirements.push(`${yearMatch[1]}+ years of relevant experience`);
    } else {
      requirements.push('Previous relevant experience required');
    }
  }

  // Remote work requirements
  requirements.push('Reliable internet connection for remote work');
  requirements.push('Ability to work independently');

  // Job-specific requirements
  if (jobTitle.toLowerCase().includes('senior')) {
    requirements.push('Senior-level experience required');
  }

  // Fallback requirements
  if (requirements.length < 3) {
    requirements.push('Strong problem-solving skills');
    requirements.push('Excellent written communication');
  }

  return requirements.slice(0, 6);
}
